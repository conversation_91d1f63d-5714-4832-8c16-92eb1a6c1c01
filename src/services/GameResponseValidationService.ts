// services/GameResponseValidationService.ts
/**
 * SERVICIO ÚNICO DE VALIDACIÓN DE RESPUESTAS
 *
 * Centraliza toda la lógica de validación de respuestas del juego
 * para eliminar duplicación entre useGameResponseValidator y SpeechInputContext
 */

import type { GameResponseType, ValidationResult } from "../models/game";

// ========== SINGLETON SERVICE ==========
class GameResponseValidationService {
  private static instance: GameResponseValidationService;
  private exactPatterns = new Map<string, GameResponseType>();
  private fuzzyPatterns: Array<{ pattern: RegExp; type: GameResponseType; confidence: number }> = [];
  private contextualPatterns = new Map<string, GameResponseType>();

  private constructor() {
    this.initializePatterns();
  }

  public static getInstance(): GameResponseValidationService {
    if (!GameResponseValidationService.instance) {
      GameResponseValidationService.instance = new GameResponseValidationService();
    }
    return GameResponseValidationService.instance;
  }

  private initializePatterns(): void {
    // Patrones exactos (máxima prioridad)
    const exactMappings: Record<string, GameResponseType> = {
      // Afirmaciones
      "sí": "yes", "si": "yes", "yes": "yes", "claro": "yes", "obvio": "yes",
      "vale": "yes", "okay": "yes", "ok": "yes", "correcto": "yes", "exacto": "yes",
      "cierto": "yes", "verdad": "yes", "por supuesto": "yes", "desde luego": "yes",
      "sin duda": "yes", "efectivamente": "yes", "así es": "yes",

      // Negaciones
      "no": "no", "nope": "no", "para nada": "no", "de ninguna manera": "no",
      "en absoluto": "no", "jamás": "no", "nunca": "no", "imposible": "no",
      "ni hablar": "no", "ni de coña": "no", "qué va": "no", "que va": "no",

      // Incertidumbre
      "tal vez": "maybe", "quizás": "maybe", "quizas": "maybe", "puede ser": "maybe",
      "es posible": "maybe", "posiblemente": "maybe", "a lo mejor": "maybe",
      "no estoy seguro": "maybe", "no estoy segura": "maybe", "depende": "maybe",

      // Desconocimiento
      "no lo sé": "unknown", "no sé": "unknown", "no se": "unknown",
      "no tengo ni idea": "unknown", "ni idea": "unknown", "no idea": "unknown",
      "no sabría decir": "unknown", "no sabria decir": "unknown"
    };

    for (const [pattern, type] of Object.entries(exactMappings)) {
      this.exactPatterns.set(pattern, type);
    }

    // Patrones contextuales
    this.contextualPatterns.set("creo que sí", "maybe");
    this.contextualPatterns.set("creo que no", "maybe");
    this.contextualPatterns.set("me parece que", "maybe");
    this.contextualPatterns.set("diría que", "maybe");

    // Patrones fuzzy con expresiones regulares
    this.fuzzyPatterns = [
      // Afirmaciones
      { pattern: /^(sí|si|yes|claro|obvio)[\s.,!]*$/i, type: "yes", confidence: 0.95 },
      { pattern: /^(vale|okay|ok)[\s.,!]*$/i, type: "yes", confidence: 0.85 },
      { pattern: /(correcto|exacto|cierto|verdad)/i, type: "yes", confidence: 0.9 },
      { pattern: /(por\s*supuesto|desde\s*luego|sin\s*duda)/i, type: "yes", confidence: 0.9 },

      // Negaciones
      { pattern: /^no[\s.,!]*$/i, type: "no", confidence: 0.95 },
      { pattern: /(para\s*nada|de\s*ninguna\s*manera|en\s*absoluto)/i, type: "no", confidence: 0.9 },
      { pattern: /(jamás|nunca|imposible)/i, type: "no", confidence: 0.85 },
      { pattern: /(ni\s*hablar|ni\s*de\s*coña|qué\s*va)/i, type: "no", confidence: 0.8 },

      // Incertidumbre
      { pattern: /(tal\s*vez|quizás?|puede\s*ser)/i, type: "maybe", confidence: 0.9 },
      { pattern: /(es\s*posible|posiblemente|a\s*lo\s*mejor)/i, type: "maybe", confidence: 0.85 },
      { pattern: /(no\s*estoy\s*segur[oa]|depende)/i, type: "maybe", confidence: 0.8 },

      // Desconocimiento
      { pattern: /(no\s*lo?\s*sé|no\s*se)/i, type: "unknown", confidence: 0.95 },
      { pattern: /(no\s*tengo\s*ni\s*idea|ni\s*idea)/i, type: "unknown", confidence: 0.9 },
      { pattern: /(no\s*sabría?\s*decir)/i, type: "unknown", confidence: 0.85 }
    ];
  }

  // ========== CORE VALIDATION ==========
  public validate(text: string): ValidationResult {
    if (!text?.trim()) {
      return {
        type: "invalid",
        confidence: 0,
        alternatives: [],
        suggestions: ["Intenta decir: Sí, No, Tal vez, o No lo sé"],
        isAmbiguous: false
      };
    }

    const normalized = text.toLowerCase().trim();
    const results: Array<{ type: GameResponseType; confidence: number; source: string }> = [];

    // 1. Búsqueda exacta (máxima prioridad)
    const exactMatch = this.exactPatterns.get(normalized);
    if (exactMatch) {
      return {
        type: exactMatch,
        confidence: 1.0,
        alternatives: [],
        isAmbiguous: false
      };
    }

    // 2. Búsqueda fuzzy con regex
    for (const { pattern, type, confidence } of this.fuzzyPatterns) {
      if (pattern.test(normalized)) {
        results.push({ type, confidence, source: 'fuzzy' });
      }
    }

    // 3. Búsqueda contextual
    for (const [context, type] of this.contextualPatterns.entries()) {
      if (normalized.includes(context)) {
        results.push({ type, confidence: 0.5, source: 'contextual' });
      }
    }

    // 4. Búsqueda por inclusión (menor prioridad)
    for (const [pattern, type] of this.exactPatterns.entries()) {
      if (normalized.includes(pattern) && pattern.length > 2) {
        results.push({ type, confidence: 0.4, source: 'inclusion' });
      }
    }

    if (results.length === 0) {
      return {
        type: "invalid",
        confidence: 0,
        alternatives: [],
        suggestions: this.generateSuggestions(normalized),
        isAmbiguous: false
      };
    }

    // Procesar resultados
    const grouped = this.groupResultsByType(results);
    const bestResult = this.selectBestResult(grouped);
    const alternatives = this.getAlternatives(grouped, bestResult.type);

    return {
      type: bestResult.type,
      confidence: bestResult.confidence,
      alternatives,
      isAmbiguous: alternatives.length > 0 && bestResult.confidence < 0.8,
      suggestions: bestResult.confidence < 0.6 ? this.generateSuggestions(normalized) : undefined
    };
  }

  // ========== UTILITY METHODS ==========
  private groupResultsByType(results: Array<{ type: GameResponseType; confidence: number; source: string }>) {
    const grouped = new Map<GameResponseType, { confidence: number; count: number }>();

    for (const result of results) {
      const existing = grouped.get(result.type);
      if (existing) {
        grouped.set(result.type, {
          confidence: Math.max(existing.confidence, result.confidence),
          count: existing.count + 1
        });
      } else {
        grouped.set(result.type, { confidence: result.confidence, count: 1 });
      }
    }

    return grouped;
  }

  private selectBestResult(grouped: Map<GameResponseType, { confidence: number; count: number }>) {
    let bestType: GameResponseType = "invalid";
    let bestScore = 0;

    for (const [type, data] of grouped.entries()) {
      const score = data.confidence * (1 + data.count * 0.1);
      if (score > bestScore) {
        bestScore = score;
        bestType = type;
      }
    }

    return { type: bestType, confidence: grouped.get(bestType)?.confidence || 0 };
  }

  private getAlternatives(grouped: Map<GameResponseType, { confidence: number; count: number }>, excludeType: GameResponseType): GameResponseType[] {
    return Array.from(grouped.entries())
      .filter(([type, data]) => type !== excludeType && data.confidence > 0.3)
      .sort(([, a], [, b]) => b.confidence - a.confidence)
      .map(([type]) => type)
      .slice(0, 2);
  }

  private generateSuggestions(input: string): string[] {
    const suggestions = ["Intenta decir: Sí, No, Tal vez, o No lo sé"];

    if (input.includes("si") || input.includes("yes")) {
      suggestions.unshift("¿Quisiste decir 'Sí'?");
    } else if (input.includes("no")) {
      suggestions.unshift("¿Quisiste decir 'No'?");
    } else if (input.includes("vez") || input.includes("quiza")) {
      suggestions.unshift("¿Quisiste decir 'Tal vez'?");
    }

    return suggestions;
  }

  // ========== PUBLIC API ==========
  public isValidResponse(text: string, minConfidence: number = 0.6): boolean {
    const result = this.validate(text);
    return result.type !== "invalid" && result.confidence >= minConfidence;
  }

  public getResponseType(text: string, minConfidence: number = 0.6): GameResponseType {
    const result = this.validate(text);
    return result.confidence >= minConfidence ? result.type : "invalid";
  }

  public getConfidenceScore(text: string): number {
    return this.validate(text).confidence;
  }

  public getAlternativeResponses(text: string): GameResponseType[] {
    return this.validate(text).alternatives;
  }

  public isAmbiguous(text: string): boolean {
    return this.validate(text).isAmbiguous;
  }

  public getSuggestions(text: string): string[] {
    return this.validate(text).suggestions || [];
  }

  public getSupportedResponses(): string[] {
    return [
      "Sí / No",
      "Tal vez / Quizás",
      "No lo sé / No sé",
      "Es posible / Puede ser"
    ];
  }

  public getResponseHelp(): string {
    return "Responde con: Sí, No, Tal vez, o No lo sé. También puedes usar variaciones como 'Claro', 'Para nada', 'Quizás', etc.";
  }
}

// ========== EXPORT SINGLETON INSTANCE ==========
export const gameResponseValidator = GameResponseValidationService.getInstance();
