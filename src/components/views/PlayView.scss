.chat-view {
  height: calc(100% - 73px);

  .menu-left {
    .enygma-logo {
      position: relative;

      img {
        width: 183px !important;
        height: 183px !important;
        border-radius: 138px;
        border: 2px solid #88FFD5;
        box-shadow: 0px 0px 12px 0px #88FFD5;
      }

      .speaking {
        animation: aura-pulse 2s infinite;
        box-shadow: 0 0 20px rgba(136, 255, 213, 0.6);
      }

      .icon-aura {
        position: absolute;
        top: -35px;
        right: -25px;
        width: 90px;
        height: 90px;
        border-radius: 61px;
        padding: 16px;
        background: #88FFD5;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;


        &.speech-indicator {
          .speech-pulse {
            background: #40e0d0;
            color: #001428;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            animation: speech-pulse 1.5s infinite;
            box-shadow: 0 2px 8px rgba(64, 224, 208, 0.4);
          }
        }
      }
    }
  }
}


.chat-view-wrapper {
  justify-content: start !important;
  max-height: 100vh;
  overflow: hidden;
  margin: 0 32px;
}

.chat-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 1rem 2rem;
}

.messages-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding-right: 0.5rem;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 20, 40, 0.3);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(64, 224, 208, 0.5);
    border-radius: 4px;

    &:hover {
      background: rgba(64, 224, 208, 0.7);
    }
  }
}

.welcome-message {
  text-align: center;
  color: #40e0d0;
  padding: 2rem;
  background: rgba(64, 224, 208, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(64, 224, 208, 0.3);

  p {
    margin: 0.5rem 0;
    line-height: 1.6;
  }
}

.message {
  display: flex;
  margin-bottom: 1rem;

  &.user-message {
    justify-content: flex-end;

    .message-content {
      background: linear-gradient(135deg, #40e0d0, #20b2aa);
      color: #001428;
      border-radius: 18px 18px 4px 18px;
      max-width: 70%;
    }
  }

  &.ai-message {
    justify-content: flex-start;

    .message-content {
      background: rgba(64, 224, 208, 0.15);
      color: #e0e0e0;
      border: 1px solid rgba(64, 224, 208, 0.3);
      border-radius: 18px 18px 18px 4px;
      max-width: 70%;
    }
  }
}

.message-content {
  padding: 0.75rem 1rem;
  display: flex;
  flex-direction: column;
  word-wrap: break-word;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.message-text {
  line-height: 1.5;
  margin-bottom: 0.25rem;

  &.typing {
    position: relative;

    &::after {
      content: '...';
      animation: typing 1.5s infinite;
    }
  }
}

.message-time {
  font-size: 0.75rem;
  opacity: 0.7;
  align-self: flex-end;
}

.chat-input-container {
  width: 100%;
  padding: 1rem 2rem;
  background: rgba(0, 20, 40, 0.9);
  border-top: 1px solid rgba(64, 224, 208, 0.3);
  flex-shrink: 0;
}

.input-wrapper {
  display: flex;
  gap: 1rem;
  width: 90%;
  margin: 0 auto;
}

.chat-input {
  flex: 1;
  padding: 0.75rem 1rem;
  background: rgba(64, 224, 208, 0.1);
  border: 1px solid rgba(64, 224, 208, 0.3);
  border-radius: 25px;
  color: #e0e0e0;
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;

  &:focus {
    border-color: #40e0d0;
    background: rgba(64, 224, 208, 0.15);
    box-shadow: 0 0 0 2px rgba(64, 224, 208, 0.2);
  }

  &::placeholder {
    color: #a0a0a0;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.send-button {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #40e0d0, #20b2aa);
  border: none;
  border-radius: 25px;
  color: #001428;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(64, 224, 208, 0.4);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

@keyframes typing {
  0%, 20% { opacity: 0; }
  50% { opacity: 1; }
  100% { opacity: 0; }
}

@keyframes aura-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(136, 255, 213, 0.6);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(136, 255, 213, 0.8);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(136, 255, 213, 0.6);
  }
}

@keyframes speech-pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .play-header {
    padding: 0.75rem 1rem;
    flex-direction: column;
    gap: 0.5rem;

    .back-button {
      align-self: flex-start;
    }

    .game-info {
      align-self: center;
      text-align: center;
    }
  }

  .chat-container {
    padding: 0.75rem 1rem;
  }

  .chat-input-container {
    padding: 0.75rem 1rem;
  }

  .message {
    &.user-message .message-content,
    &.ai-message .message-content {
      max-width: 85%;
    }
  }

  .input-wrapper {
    gap: 0.5rem;
  }

  .send-button {
    min-width: 70px;
    padding: 0.75rem 1rem;
  }
}
